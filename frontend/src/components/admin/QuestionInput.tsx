import { useState } from "react";
import {
  QuestionInput as QuestionInputType,
  LikertLabelMap,
  FreeTextTableColumn,
  LikertTableColumn,
} from "../../types/exercise.types";
import { v4 as uuid } from "uuid";
import { But<PERSON> } from "../ui/Button";
import Checkbox from "../ui/input/Checkbox";
import SelectInput from "../ui/input/SelectInput";
import TextAreaInput from "../ui/input/TextAreaInput";
import RichTextEditor from "../ui/input/RichTextEditor";
import { uploadImageWithFallback } from "../../utils/imageUpload";
import TextInput from "../ui/input/TextInput";
import NumberInput from "../ui/input/NumberInput";

const QuestionTypeOptions = [
  { value: "free_text", label: "Free Text" },
  { value: "likert", label: "Likert" },
  { value: "task", label: "Task" },
  { value: "table", label: "Table" },
  { value: "graph_2d", label: "Graph 2D" },
];

const QuestionInput = ({
  question,
  onChange,
  onDelete,
}: {
  question: QuestionInputType;
  onChange: (question: QuestionInputType) => void;
  onDelete?: () => void;
}) => {
  const [questionInput, setQuestionInput] =
    useState<QuestionInputType>(question);
  console.log("QuestionInput", questionInput);

  return (
    <div>
      <div className="flex gap-4 items-end">
        <SelectInput
          label="Question Type"
          options={QuestionTypeOptions}
          value={questionInput.type}
          onChange={(value) => {
            setQuestionInput({
              ...questionInput,
              type: value as any,
            });
          }}
        />
        <Checkbox
          label="Is Required"
          id="isRequired"
          className="shrink-0"
          checked={questionInput.required}
          onChange={(checked) => {
            setQuestionInput({
              ...questionInput,
              required: checked,
            });
          }}
        />
      </div>
      <TextAreaInput
        label="Question Prompt"
        placeholder="Enter question prompt"
        value={questionInput.prompt}
        onChange={(value) => {
          setQuestionInput({
            ...questionInput,
            prompt: value,
          });
        }}
      />
      <RichTextEditor
        label="Question Description"
        placeholder="Enter question description with rich text formatting..."
        value={questionInput.description || ""}
        onChange={(value) => {
          setQuestionInput({
            ...questionInput,
            description: value,
          });
        }}
        height="120px"
        enableImages={true}
        onImageUpload={uploadImageWithFallback}
      />
      {questionInput.type === "free_text" && (
        <div>
          <TextInput
            label="Placeholder"
            placeholder="Enter placeholder text"
            value={questionInput.placeholder}
            onChange={(value) => {
              setQuestionInput({
                ...questionInput,
                placeholder: value,
              });
            }}
          />
        </div>
      )}
      {questionInput.type === "likert" && (
        <>
          <div>
            <div className="flex gap-2 items-center mt-2">
              <SelectInput
                label="Response Mode"
                options={[
                  { value: "single", label: "Single" },
                  { value: "multi", label: "Multi" },
                ]}
                value={questionInput.responseMode}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    responseMode: value as any,
                    multiResponseLabels: [],
                  });
                }}
              />
              <NumberInput
                label="Scale Min"
                value={questionInput.scaleMin}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    scaleMin: value,
                  });
                }}
              />
              <NumberInput
                label="Scale Max"
                value={questionInput.scaleMax}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    scaleMax: value,
                  });
                }}
              />
            </div>
            {/* Likert Scale Labels */}
            <div className="mt-4 space-y-4">
              <div className="flex gap-4">
                <div className="flex-1">
                  <TextInput
                    label={`Min Label (${questionInput.scaleMin})`}
                    placeholder="e.g., Strongly Disagree"
                    value={questionInput.labels?.[questionInput.scaleMin] || ""}
                    onChange={(value) => {
                      setQuestionInput({
                        ...questionInput,
                        labels: {
                          ...questionInput.labels,
                          [questionInput.scaleMin]: value,
                        },
                      });
                    }}
                  />
                </div>
                <div className="flex-1">
                  <TextInput
                    label={`Max Label (${questionInput.scaleMax})`}
                    placeholder="e.g., Strongly Agree"
                    value={questionInput.labels?.[questionInput.scaleMax] || ""}
                    onChange={(value) => {
                      setQuestionInput({
                        ...questionInput,
                        labels: {
                          ...questionInput.labels,
                          [questionInput.scaleMax]: value,
                        },
                      });
                    }}
                  />
                </div>
              </div>

              {/* Additional Scale Point Labels */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Scale Point Labels
                </label>
                <div className="space-y-2">
                  {Object.entries(questionInput.labels || {})
                    .filter(
                      ([point]) =>
                        Number(point) !== questionInput.scaleMin &&
                        Number(point) !== questionInput.scaleMax
                    )
                    .map(([point, label]) => (
                      <div key={point} className="flex gap-2 items-center">
                        <NumberInput
                          placeholder="Point"
                          value={Number(point)}
                          min={questionInput.scaleMin + 1}
                          max={questionInput.scaleMax - 1}
                          className="w-24"
                          onChange={(newPoint) => {
                            if (!newPoint) return;
                            const newLabels = {
                              ...questionInput.labels,
                            } as LikertLabelMap;
                            delete newLabels[Number(point)];
                            newLabels[newPoint] = label;
                            setQuestionInput({
                              ...questionInput,
                              labels: newLabels,
                            });
                          }}
                        />
                        <TextInput
                          placeholder="Label"
                          value={label}
                          className="flex-1"
                          onChange={(value) => {
                            setQuestionInput({
                              ...questionInput,
                              labels: {
                                ...questionInput.labels,
                                [Number(point)]: value,
                              },
                            });
                          }}
                        />
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => {
                            const newLabels = {
                              ...questionInput.labels,
                            } as LikertLabelMap;
                            delete newLabels[Number(point)];
                            setQuestionInput({
                              ...questionInput,
                              labels: newLabels,
                            });
                          }}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                </div>
                <Button
                  variant="secondary"
                  size="sm"
                  className="mt-2"
                  onClick={() => {
                    const existingPoints = Object.keys(
                      questionInput.labels || {}
                    ).map(Number);
                    const availablePoints = Array.from(
                      {
                        length:
                          questionInput.scaleMax - questionInput.scaleMin - 1,
                      },
                      (_, i) => questionInput.scaleMin + i + 1
                    ).filter((point) => !existingPoints.includes(point));

                    if (availablePoints.length > 0) {
                      const nextPoint = availablePoints[0];
                      setQuestionInput({
                        ...questionInput,
                        labels: {
                          ...questionInput.labels,
                          [nextPoint]: "",
                        },
                      });
                    }
                  }}
                >
                  Add Scale Point Label
                </Button>
              </div>
            </div>
            {/* Multi Response Labels */}
            {questionInput.responseMode === "multi" && (
              <div className="flex gap-2 items-center mt-2">
                <label
                  htmlFor="multiResponseLabels"
                  className="block text-sm font-medium text-gray-700"
                >
                  Multi Response Labels (comma separated)
                </label>
                <TextInput
                  placeholder="e.g., Option 1, Option 2"
                  value={questionInput.multiResponseLabels?.join(", ") || ""}
                  onChange={(value) => {
                    setQuestionInput({
                      ...questionInput,
                      multiResponseLabels: value.split(", "),
                    });
                  }}
                  className="flex-1"
                />
              </div>
            )}
            <div className="grid grid-cols-4 gap-2 items-center mt-2"></div>
            <div className="flex gap-2 items-center mt-2"></div>
          </div>
        </>
      )}
      {questionInput.type === "table" && (
        <div className="space-y-4">
          <div className="flex gap-2 items-center">
            <SelectInput
              label="Row Mode"
              options={[
                { value: "", label: "Select a row mode" },
                { value: "dynamic", label: "Dynamic" },
                { value: "fixed", label: "Fixed" },
              ]}
              value={questionInput.rowMode}
              onChange={(value) => {
                setQuestionInput({
                  ...questionInput,
                  rowMode: value as any,
                });
              }}
            />
            {questionInput.rowMode === "fixed" && (
              <NumberInput
                label="Number of Rows"
                value={questionInput.fixedRows}
                min={1}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    fixedRows: value || 1,
                  });
                }}
              />
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Table Columns
            </label>
            <div className="space-y-4">
              {(questionInput.columns || []).map((column, index) => (
                <div key={column.id} className="flex gap-2 items-end">
                  <TextInput
                    label="Header"
                    placeholder="Column header"
                    value={column.header}
                    onChange={(value) => {
                      const newColumns = [...(questionInput.columns || [])];
                      newColumns[index] = {
                        ...column,
                        header: value,
                      };
                      setQuestionInput({
                        ...questionInput,
                        columns: newColumns,
                      });
                    }}
                  />
                  <SelectInput
                    label="Column Type"
                    options={[
                      { value: "free_text", label: "Free Text" },
                      { value: "likert", label: "Likert" },
                    ]}
                    value={column.columnType}
                    onChange={(value) => {
                      const newColumns = [...(questionInput.columns || [])];
                      if (value === "likert") {
                        newColumns[index] = {
                          ...column,
                          columnType: "likert",
                          scaleMin: 1,
                          scaleMax: 5,
                          labels: {},
                        };
                      } else {
                        newColumns[index] = {
                          ...column,
                          columnType: "free_text",
                          placeholder: "",
                        };
                      }
                      setQuestionInput({
                        ...questionInput,
                        columns: newColumns,
                      });
                    }}
                  />
                  {column.columnType === "free_text" && (
                    <TextInput
                      label="Placeholder"
                      placeholder="Enter placeholder text"
                      value={(column as FreeTextTableColumn).placeholder}
                      onChange={(value) => {
                        const newColumns = [...(questionInput.columns || [])];
                        newColumns[index] = {
                          ...column,
                          placeholder: value,
                        };
                        setQuestionInput({
                          ...questionInput,
                          columns: newColumns,
                        });
                      }}
                    />
                  )}
                  {column.columnType === "likert" && (
                    <>
                      <NumberInput
                        label="Scale Min"
                        value={(column as LikertTableColumn).scaleMin}
                        onChange={(value) => {
                          const newColumns = [...(questionInput.columns || [])];
                          newColumns[index] = {
                            ...column,
                            scaleMin: value || 1,
                          };
                          setQuestionInput({
                            ...questionInput,
                            columns: newColumns,
                          });
                        }}
                      />
                      <NumberInput
                        label="Scale Max"
                        value={(column as LikertTableColumn).scaleMax}
                        onChange={(value) => {
                          const newColumns = [...(questionInput.columns || [])];
                          newColumns[index] = {
                            ...column,
                            scaleMax: value || 5,
                          };
                          setQuestionInput({
                            ...questionInput,
                            columns: newColumns,
                          });
                        }}
                      />
                    </>
                  )}
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => {
                      const newColumns = (questionInput.columns || []).filter(
                        (_, i) => i !== index
                      );
                      setQuestionInput({
                        ...questionInput,
                        columns: newColumns,
                      });
                    }}
                  >
                    Remove
                  </Button>
                </div>
              ))}
            </div>
            <Button
              variant="secondary"
              size="sm"
              className="mt-2"
              onClick={() => {
                const newColumn: FreeTextTableColumn = {
                  id: `col-${uuid()}`,
                  header: "",
                  columnType: "free_text",
                  placeholder: "",
                };
                setQuestionInput({
                  ...questionInput,
                  columns: [...(questionInput.columns || []), newColumn],
                });
              }}
            >
              Add Column
            </Button>
          </div>
        </div>
      )}
      {questionInput.type === "graph_2d" && (
        <div className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <TextInput
                label="X-Axis Label"
                placeholder="Enter x-axis label"
                value={questionInput.xAxisLabel}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    xAxisLabel: value,
                  });
                }}
              />
            </div>
            <div className="flex-1">
              <TextInput
                label="Y-Axis Label"
                placeholder="Enter y-axis label"
                value={questionInput.yAxisLabel}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    yAxisLabel: value,
                  });
                }}
              />
            </div>
          </div>
          <div className="flex gap-4">
            <div className="flex gap-2">
              <NumberInput
                label="X-Axis Min"
                value={questionInput.xAxisMin}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    xAxisMin: value,
                  });
                }}
              />
              <NumberInput
                label="X-Axis Max"
                value={questionInput.xAxisMax}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    xAxisMax: value,
                  });
                }}
              />
            </div>
            <div className="flex gap-2">
              <NumberInput
                label="Y-Axis Min"
                value={questionInput.yAxisMin}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    yAxisMin: value,
                  });
                }}
              />
              <NumberInput
                label="Y-Axis Max"
                value={questionInput.yAxisMax}
                onChange={(value) => {
                  setQuestionInput({
                    ...questionInput,
                    yAxisMax: value,
                  });
                }}
              />
            </div>
          </div>
        </div>
      )}
      <div className="flex gap-4 items-center mt-4">
        <Button
          variant="primary"
          size="sm"
          className="mt-2"
          onClick={() =>
            onChange({
              ...questionInput,
              isEditing: false,
            })
          }
        >
          Save Question
        </Button>
        {onDelete && (
          <Button
            variant="danger"
            size="sm"
            className="mt-2"
            onClick={onDelete}
          >
            Delete Question
          </Button>
        )}
      </div>
    </div>
  );
};

export default QuestionInput;
