import { useState, useEffect } from "react";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import { DataTable } from "../ui/DataTable";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Column } from "../../types/components.types";
import { useAdmin } from "../../context/AdminContext";
import {
  Calendar,
  Play,
  BookOpen,
  Eye,
  BicepsFlexed,
  HeartPlus,
} from "lucide-react";
import {
  generatePeriodization,
  PeriodizationEntry,
} from "../../utils/periodization";
import { Label } from "../ui/Label";

const AdminPeriodization = () => {
  const {
    allExercises,
    allVisualizations,
    loading,
    error,
    clearError,
    fetchAllExercises,
    fetchAllVisualizations,
  } = useAdmin();
  // Season dates
  const [offSeasonStartDate, setOffSeasonStartDate] = useState<string>("");
  const [offSeasonEndDate, setOffSeasonEndDate] = useState<string>("");
  const [prepStartDate, setPrepStartDate] = useState<string>("");
  const [prepEndDate, setPrepEndDate] = useState<string>("");
  const [preCompStartDate, setPreCompStartDate] = useState<string>("");
  const [preCompEndDate, setPreCompEndDate] = useState<string>("");
  const [compStartDate, setCompStartDate] = useState<string>("");
  const [compEndDate, setCompEndDate] = useState<string>("");

  // Scheduling options
  const [scheduleMentalToughness, setScheduleMentalToughness] =
    useState<boolean>(true);
  const [scheduleMentalWellness, setScheduleMentalWellness] =
    useState<boolean>(true);

  const [periodizationData, setPeriodizationData] = useState<
    PeriodizationEntry[]
  >([]);
  const [errors, setErrors] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Set default dates
  useEffect(() => {
    const today = new Date();
    const offSeasonStart = new Date(today);
    const offSeasonEnd = new Date(today);
    offSeasonEnd.setDate(today.getDate() + 4 * 7); // 4 weeks later

    const prepStart = new Date(offSeasonEnd);
    prepStart.setDate(offSeasonEnd.getDate() + 1);
    const prepEnd = new Date(prepStart);
    prepEnd.setDate(prepStart.getDate() + 4 * 7); // 4 weeks later

    const preCompStart = new Date(prepEnd);
    preCompStart.setDate(prepEnd.getDate() + 1);
    const preCompEnd = new Date(preCompStart);
    preCompEnd.setDate(preCompStart.getDate() + 2 * 7); // 2 weeks later

    const compStart = new Date(preCompEnd);
    compStart.setDate(preCompEnd.getDate() + 1);
    const compEnd = new Date(compStart);
    compEnd.setDate(compStart.getDate() + 1 * 7); // 1 week later

    setOffSeasonStartDate(offSeasonStart.toISOString().split("T")[0]);
    setOffSeasonEndDate(offSeasonEnd.toISOString().split("T")[0]);
    setPrepStartDate(prepStart.toISOString().split("T")[0]);
    setPrepEndDate(prepEnd.toISOString().split("T")[0]);
    setPreCompStartDate(preCompStart.toISOString().split("T")[0]);
    setPreCompEndDate(preCompEnd.toISOString().split("T")[0]);
    setCompStartDate(compStart.toISOString().split("T")[0]);
    setCompEndDate(compEnd.toISOString().split("T")[0]);

    if (allExercises.length === 0 || allVisualizations.length === 0) {
      fetchAllExercises();
      fetchAllVisualizations();
    }
  }, []);

  const handleGeneratePeriodization = async () => {
    if (
      !offSeasonStartDate ||
      !offSeasonEndDate ||
      !prepStartDate ||
      !prepEndDate ||
      !preCompStartDate ||
      !preCompEndDate ||
      !compStartDate ||
      !compEndDate
    ) {
      return;
    }

    setIsGenerating(true);
    setErrors([]);

    const result = generatePeriodization(
      new Date(offSeasonStartDate),
      new Date(offSeasonEndDate),
      new Date(prepStartDate),
      new Date(prepEndDate),
      new Date(preCompStartDate),
      new Date(preCompEndDate),
      new Date(compStartDate),
      new Date(compEndDate),
      allExercises,
      scheduleMentalToughness,
      scheduleMentalWellness
    );

    setPeriodizationData(result.entries);
    setErrors(result.errors);
    setIsGenerating(false);
  };

  const columns: Column<PeriodizationEntry>[] = [
    {
      header: "Week",
      accessorKey: "week",
      cell: (entry) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-blue-600 mr-2" />
          <span className="font-medium">Week {entry.week}</span>
        </div>
      ),
    },
    {
      header: "Assignment Date",
      accessorKey: "assignmentDate",
      cell: (entry) => (
        <span className="text-sm text-gray-900">
          {new Date(entry.assignmentDate).toLocaleDateString("en-US", {
            weekday: "short",
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </span>
      ),
    },
    {
      header: "Type",
      accessorKey: "exerciseType",
      cell: (entry) => (
        <div className="flex items-center">
          {entry.exerciseType === "MT exercise" ? (
            <Label icon={BicepsFlexed} color="orange" text="MT Exercise" />
          ) : entry.exerciseType === "MW exercise" ? (
            <Label icon={HeartPlus} color="green" text="MW Exercise" />
          ) : (
            <Label icon={Eye} color="purple" text="Visualization" />
          )}
        </div>
      ),
    },
    {
      header: "Exercise/Visualization",
      accessorKey: "exerciseName",
      cell: (entry) => (
        <div>
          <p className="text-sm font-medium text-gray-900">
            {entry.exerciseName}
          </p>
          <p className="text-xs text-gray-500">ID: {entry.exerciseId}</p>
        </div>
      ),
    },
  ];

  if (loading.exercises || loading.visualizations) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Periodization Testing"
        description="Test the periodization algorithm by generating assignment schedules"
      />

      {error && <ErrorMessage message={error} onDismiss={clearError} />}

      {/* Configuration Form */}
      <Card>
        <CardHeader title="Periodization Configuration" />
        <div className="p-6 pt-0">
          {/* Season Dates */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Season Dates
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="offSeasonStartDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Off Season Start Date
                  </label>
                  <input
                    type="date"
                    id="offSeasonStartDate"
                    value={offSeasonStartDate}
                    onChange={(e) => setOffSeasonStartDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="offSeasonEndDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Off Season End Date
                  </label>
                  <input
                    type="date"
                    id="offSeasonEndDate"
                    value={offSeasonEndDate}
                    onChange={(e) => setOffSeasonEndDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="prepStartDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Prep Start Date
                  </label>
                  <input
                    type="date"
                    id="prepStartDate"
                    value={prepStartDate}
                    onChange={(e) => setPrepStartDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="prepEndDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Prep End Date
                  </label>
                  <input
                    type="date"
                    id="prepEndDate"
                    value={prepEndDate}
                    onChange={(e) => setPrepEndDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="preCompStartDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Pre-Comp Start Date
                  </label>
                  <input
                    type="date"
                    id="preCompStartDate"
                    value={preCompStartDate}
                    onChange={(e) => setPreCompStartDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="preCompEndDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Pre-Comp End Date
                  </label>
                  <input
                    type="date"
                    id="preCompEndDate"
                    value={preCompEndDate}
                    onChange={(e) => setPreCompEndDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="compStartDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Competition Start Date
                  </label>
                  <input
                    type="date"
                    id="compStartDate"
                    value={compStartDate}
                    onChange={(e) => setCompStartDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="compEndDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Competition End Date
                  </label>
                  <input
                    type="date"
                    id="compEndDate"
                    value={compEndDate}
                    onChange={(e) => setCompEndDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Scheduling Options */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Scheduling Options
              </h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    id="scheduleMentalToughness"
                    type="checkbox"
                    checked={scheduleMentalToughness}
                    onChange={(e) =>
                      setScheduleMentalToughness(e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="scheduleMentalToughness"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Schedule Mental Toughness Exercises
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="scheduleMentalWellness"
                    type="checkbox"
                    checked={scheduleMentalWellness}
                    onChange={(e) =>
                      setScheduleMentalWellness(e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="scheduleMentalWellness"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Schedule Mental Wellness Exercises
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {errors.length > 0 && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <h4 className="text-sm font-medium text-red-800 mb-2">
                Validation Errors:
              </h4>
              <ul className="text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="mt-6">
            <Button
              onClick={handleGeneratePeriodization}
              disabled={
                !offSeasonStartDate ||
                !offSeasonEndDate ||
                !prepStartDate ||
                !prepEndDate ||
                !preCompStartDate ||
                !preCompEndDate ||
                !compStartDate ||
                !compEndDate ||
                isGenerating
              }
              className="flex items-center"
            >
              <Play className="h-4 w-4 mr-2" />
              {isGenerating ? "Generating..." : "Generate Periodization"}
            </Button>
          </div>
        </div>
      </Card>

      {/* Available Resources Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-4">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Available Exercises
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allExercises.length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Available Visualizations
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allVisualizations.length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Periodization Results */}
      {periodizationData.length > 0 && (
        <Card>
          <CardHeader
            title={`Periodization Schedule (${periodizationData.length} assignments)`}
          />
          <div className="p-6 pt-0">
            <DataTable
              columns={columns}
              data={periodizationData}
              className="w-full"
            />
          </div>
        </Card>
      )}

      {/* Empty State */}
      {periodizationData.length === 0 &&
        offSeasonStartDate &&
        offSeasonEndDate && (
          <Card className="p-8">
            <div className="text-center">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No periodization generated
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Click "Generate Periodization" to create an assignment schedule.
              </p>
            </div>
          </Card>
        )}
    </div>
  );
};

export default AdminPeriodization;
