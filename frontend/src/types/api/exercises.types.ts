import { Question, TextBlock } from "../exercise.types";

export interface Exercise {
  id: string;
  name: string;
  description: string;
  questions: Question[];
  textBlocks?: TextBlock[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateExerciseData {
  id?: string;
  name: string;
  description: string;
  questions: Omit<Question, "id">[];
  textBlocks?: Omit<TextBlock, "id">[];
}
